"use server"

// User management actions for Trustay API
import { cookies } from 'next/headers';
import { UserProfile, UpdateProfileRequest } from '../types/types';

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

// API Error class (local utility, not exported due to "use server" constraint)
class ApiError extends Error {
  constructor(
    message: string,
    public statusCode: number,
    public response?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// Helper function to make API calls
async function apiCall<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const cookieStore = await cookies();
  const accessToken = cookieStore.get('accessToken')?.value;

  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...(options.headers as Record<string, string>),
  };

  if (accessToken) {
    headers['Authorization'] = `Bearer ${accessToken}`;
  }

  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers,
  });

  let data;
  try {
    data = await response.json();
  } catch {
    data = null;
  }

  if (!response.ok) {
    throw new ApiError(
      data?.message || data?.error || `HTTP ${response.status}`,
      response.status,
      data
    );
  }

  return data;
}

// Get user profile
export const getUserProfile = async (): Promise<UserProfile> => {
  return await apiCall<UserProfile>('/api/users/profile', {
    method: 'GET',
  });
};

// Update user profile
export const updateUserProfile = async (
  profileData: UpdateProfileRequest
): Promise<UserProfile> => {
  return await apiCall<UserProfile>('/api/users/profile', {
    method: 'PUT',
    body: JSON.stringify(profileData),
  });
};
